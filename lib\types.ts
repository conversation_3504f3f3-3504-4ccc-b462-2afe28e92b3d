// RIASEC Types
export type RiasecType = 'R' | 'I' | 'A' | 'S' | 'E' | 'C';

// Question interface
export interface Question {
  id: number;
  text: string;
  riasec_type: RiasecType;
}

// Answer interface for storing user responses
export interface Answer {
  questionId: number;
  score: number; // 1-5 Likert scale
}

// RIASEC scores interface
export interface RiasecScores {
  R: number; // Realistic
  I: number; // Investigative
  A: number; // Artistic
  S: number; // Social
  E: number; // Enterprising
  C: number; // Conventional
}

// RIASEC descriptions
export interface RiasecDescription {
  type: RiasecType;
  name: string;
  description: string;
}
