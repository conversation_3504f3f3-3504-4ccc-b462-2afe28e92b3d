'use client';

import { useRef } from 'react';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'chart.js';
import { Radar } from 'react-chartjs-2';
import { RiasecScores } from '@/lib/types';

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
);

interface RadarChartProps {
  scores: RiasecScores;
}

export default function RadarChart({ scores }: RadarChartProps) {
  const chartRef = useRef<ChartJS<'radar'>>(null);

  const data = {
    labels: [
      'Realistic (R)',
      'Investigative (I)', 
      'Artistic (A)',
      'Social (S)',
      'Enterprising (E)',
      'Conventional (C)'
    ],
    datasets: [
      {
        label: 'Skor RIASEC Anda',
        data: [scores.R, scores.I, scores.A, scores.S, scores.E, scores.C],
        backgroundColor: 'rgba(99, 102, 241, 0.2)',
        borderColor: 'rgba(99, 102, 241, 1)',
        borderWidth: 3,
        pointBackgroundColor: 'rgba(99, 102, 241, 1)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          font: {
            size: 14,
            weight: 'bold' as const,
          },
          color: '#374151',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(99, 102, 241, 1)',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            // Convert score to interest level
            const score = context.parsed.r;
            let level = 'Rendah';
            if (score >= 20) level = 'Tinggi';
            else if (score >= 15) level = 'Sedang';

            return `Tingkat Minat: ${level}`;
          }
        }
      },
    },
    scales: {
      r: {
        beginAtZero: true,
        min: 0,
        max: 25, // Maximum possible score (5 questions × 5 points each)
        ticks: {
          display: false, // Hide the numbers on the radial scale
        },
        grid: {
          color: 'rgba(156, 163, 175, 0.3)',
        },
        angleLines: {
          color: 'rgba(156, 163, 175, 0.3)',
        },
        pointLabels: {
          font: {
            size: 13,
            weight: 'bold' as const,
          },
          color: '#374151',
        },
      },
    },
  };

  return (
    <div className="w-full h-96 relative">
      <Radar ref={chartRef} data={data} options={options} />
    </div>
  );
}
