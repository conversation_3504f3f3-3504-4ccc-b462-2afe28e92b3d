import { GoogleGenAI, Type } from "@google/genai";
import { RiasecScores } from './types';

// Interface untuk response dari Gemini AI
export interface GeminiProfileResponse {
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workStyle: string;
}

// Fungsi untuk membuat prompt yang sesuai dengan format yang diminta
function createRiasecPrompt(scores: RiasecScores): string {
  // Konversi skor dari skala 5-25 ke skala 1-100 untuk lebih mudah dipahami AI
  const convertedScores = {
    R: Math.round((scores.R / 25) * 100),
    I: Math.round((scores.I / 25) * 100),
    A: Math.round((scores.A / 25) * 100),
    S: Math.round((scores.S / 25) * 100),
    E: Math.round((scores.E / 25) * 100),
    C: Math.round((scores.C / 25) * 100)
  };

  return `Anda adalah seorang konselor karier ahli yang berspesialisasi dalam analisis kepribadian model RIASEC (Holland Codes).

Analisis skor RIASEC berikut dan hasilkan profil kepribadian dan karier yang ringkas dan terstruktur. Skor tertinggi menunjukkan minat yang paling dominan.

Data Skor RIASEC (skala 1-100):
R (Realistic): ${convertedScores.R}
I (Investigative): ${convertedScores.I}
A (Artistic): ${convertedScores.A}
S (Social): ${convertedScores.S}
E (Enterprising): ${convertedScores.E}
C (Conventional): ${convertedScores.C}

Instruksi:
1. Fokus pada 2-3 skor tertinggi untuk menentukan profil dominan
2. Berikan interpretasi yang personal dan spesifik berdasarkan kombinasi skor
3. Gunakan bahasa Indonesia yang natural dan mudah dipahami
4. Pastikan rekomendasi karier realistis dan relevan dengan konteks Indonesia
5. Kekuatan harus spesifik dan actionable, dijelaskan dengan singkat

Berikan profil yang unik dan personal berdasarkan kombinasi skor yang spesifik ini.`;
}

// Schema untuk structured output menggunakan Google GenAI
const profileResponseSchema = {
  type: Type.OBJECT,
  properties: {
    profileTitle: {
      type: Type.STRING,
      description: "Judul profil singkat yang menarik berdasarkan 2-3 kode RIASEC tertinggi seperti (The Innovative Thinker)"
    },
    profileDescription: {
      type: Type.STRING,
      description: "Deskripsi kepribadian dalam DUA kalimat ringkas berdasarkan profil dominan"
    },
    strengths: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 kekuatan utama berdasarkan profil RIASEC, dijelaskan dengan singkat"
    },
    careerSuggestions: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 rekomendasi karier yang sesuai dengan profil"
    },
    workStyle: {
      type: Type.STRING,
      description: "Gaya kerja ideal dalam SATU kalimat"
    }
  },
  propertyOrdering: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle"],
  required: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle"]
};

// Service class untuk Gemini AI dengan structured output
export class GeminiProfileService {
  private ai: GoogleGenAI;

  constructor() {
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;

    if (!apiKey) {
      throw new Error('NEXT_PUBLIC_GEMINI_API_KEY is not set in environment variables');
    }

    this.ai = new GoogleGenAI({
      apiKey: apiKey
    });
  }

  async generateProfile(scores: RiasecScores): Promise<GeminiProfileResponse> {
    try {
      const prompt = createRiasecPrompt(scores);

      // Menggunakan structured output dengan schema yang proper
      const response = await this.ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: prompt,
        config: {
          responseMimeType: "application/json",
          responseSchema: profileResponseSchema,
          temperature: 0.7, // Sedikit kreativitas tapi tetap konsisten
          topP: 0.8,
          topK: 40,
        }
      });

      // Response sudah berupa JSON yang valid berkat structured output
      const responseText = response.text;
      if (!responseText) {
        throw new Error('Empty response from Gemini AI');
      }

      const profileData: GeminiProfileResponse = JSON.parse(responseText);

      // Validasi response (seharusnya tidak perlu karena schema sudah enforce structure)
      if (!profileData.profileTitle || !profileData.profileDescription ||
          !profileData.strengths || !profileData.careerSuggestions ||
          !profileData.workStyle) {
        throw new Error('Invalid response structure from Gemini AI');
      }

      // Pastikan array memiliki minimal 5 item (schema seharusnya sudah enforce ini)
      if (profileData.strengths.length < 5) {
        console.warn('Gemini returned less than 5 strengths, padding with generic ones');
        while (profileData.strengths.length < 5) {
          profileData.strengths.push('Kemampuan adaptasi yang baik');
        }
      }

      if (profileData.careerSuggestions.length < 5) {
        console.warn('Gemini returned less than 5 career suggestions, padding with generic ones');
        while (profileData.careerSuggestions.length < 5) {
          profileData.careerSuggestions.push('Konsultan profesional');
        }
      }

      return profileData;
    } catch (error) {
      console.error('Error generating profile with Gemini AI:', error);

      // Fallback ke response default jika AI gagal
      return this.getFallbackProfile(scores);
    }
  }

  // Note: cleanJsonResponse function tidak lagi diperlukan karena structured output
  // menggunakan schema yang memastikan response selalu berupa JSON yang valid

  // Fallback profile jika Gemini AI gagal
  private getFallbackProfile(scores: RiasecScores): GeminiProfileResponse {
    const maxScore = Math.max(...Object.values(scores));
    const dominantType = Object.entries(scores)
      .find(([_, score]) => score === maxScore)?.[0] || 'R';

    const fallbackProfiles: Record<string, GeminiProfileResponse> = {
      'R': {
        profileTitle: 'The Practical Implementer',
        profileDescription: 'Anda adalah seseorang yang praktis dan suka bekerja dengan tangan. Anda menikmati aktivitas fisik dan hasil yang nyata.',
        strengths: ['Keterampilan teknis', 'Pemecahan masalah praktis', 'Kemandirian', 'Ketahanan fisik', 'Orientasi hasil'],
        careerSuggestions: ['Teknisi', 'Insinyur', 'Mekanik', 'Arsitek', 'Pilot'],
        workStyle: 'Lingkungan kerja yang terstruktur dengan tugas-tugas konkret dan hasil yang terukur.'
      },
      'I': {
        profileTitle: 'The Analytical Thinker',
        profileDescription: 'Anda adalah seseorang yang analitis dan suka memecahkan masalah kompleks. Anda menikmati penelitian dan bekerja dengan ide-ide.',
        strengths: ['Kemampuan analitis', 'Pemikiran kritis', 'Penelitian', 'Pemecahan masalah', 'Objektivitas'],
        careerSuggestions: ['Peneliti', 'Ilmuwan', 'Dokter', 'Psikolog', 'Analis Data'],
        workStyle: 'Lingkungan kerja yang tenang dengan waktu untuk berpikir dan menganalisis secara mendalam.'
      },
      'A': {
        profileTitle: 'The Creative Innovator',
        profileDescription: 'Anda adalah seseorang yang kreatif dan ekspresif. Anda menikmati kegiatan seni dan menciptakan sesuatu yang baru.',
        strengths: ['Kreativitas', 'Ekspresi diri', 'Inovasi', 'Sensitivitas estetika', 'Fleksibilitas'],
        careerSuggestions: ['Desainer', 'Seniman', 'Penulis', 'Musisi', 'Fotografer'],
        workStyle: 'Lingkungan kerja yang fleksibel dengan kebebasan untuk berekspresi dan berkreasi.'
      },
      'S': {
        profileTitle: 'The People Helper',
        profileDescription: 'Anda adalah seseorang yang peduli dan suka membantu orang lain. Anda menikmati interaksi sosial dan membuat perbedaan.',
        strengths: ['Empati', 'Komunikasi', 'Kepemimpinan', 'Kerja tim', 'Motivasi orang lain'],
        careerSuggestions: ['Guru', 'Konselor', 'Perawat', 'Pekerja Sosial', 'HR Manager'],
        workStyle: 'Lingkungan kerja yang kolaboratif dengan banyak interaksi interpersonal yang bermakna.'
      },
      'E': {
        profileTitle: 'The Ambitious Leader',
        profileDescription: 'Anda adalah seseorang yang ambisius dan suka memimpin. Anda menikmati tantangan bisnis dan mempengaruhi orang lain.',
        strengths: ['Kepemimpinan', 'Persuasi', 'Pengambilan risiko', 'Orientasi hasil', 'Visi strategis'],
        careerSuggestions: ['Manajer', 'Pengusaha', 'Sales Manager', 'Konsultan', 'Direktur'],
        workStyle: 'Lingkungan kerja yang dinamis dengan peluang untuk memimpin dan mempengaruhi keputusan.'
      },
      'C': {
        profileTitle: 'The Systematic Organizer',
        profileDescription: 'Anda adalah seseorang yang terorganisir dan detail-oriented. Anda menikmati struktur dan prosedur yang jelas.',
        strengths: ['Organisasi', 'Perhatian detail', 'Keandalan', 'Efisiensi', 'Konsistensi'],
        careerSuggestions: ['Akuntan', 'Administrator', 'Auditor', 'Analis Keuangan', 'Sekretaris'],
        workStyle: 'Lingkungan kerja yang terstruktur dengan prosedur dan sistem yang jelas dan konsisten.'
      }
    };

    return fallbackProfiles[dominantType] || fallbackProfiles['R'];
  }
}
